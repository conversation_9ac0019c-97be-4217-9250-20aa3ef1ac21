import type { ComponentProps } from "react"
import { Tabs } from "@base-ui-components/react/tabs"

export type TabsRootProps = ComponentProps<typeof Tabs.Root>
export type TabsListProps = ComponentProps<typeof Tabs.List>
export type TabProps = ComponentProps<typeof Tabs.Tab> & {
  variant?: "default" | "full"
  align?: "left" | "center" | "right"
}
export type TabsIndicatorProps = ComponentProps<typeof Tabs.Indicator>
export type TabsPanelProps = ComponentProps<typeof Tabs.Panel>
